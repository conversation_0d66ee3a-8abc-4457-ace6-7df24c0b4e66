import { CommonModule } from '@angular/common';
import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  NgZone,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation,
  DestroyRef,
  inject,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject } from 'rxjs';
import { CardsComponent, PromptBarComponent, IconsComponent } from '@awe/play-comp-library';
import { MarkdownModule } from 'ngx-markdown';
import { VerticalStepperComponent } from '../vertical-stepper/vertical-stepper.component';

import { GenerationAccordionComponent, GenerationResult } from '../generation-accordion/generation-accordion.component';
import { StepperStateService } from '../../services/stepper-state.service';
import { RegenerationIntegrationService } from '../../services/regeneration-integration.service';
import { EnhancedSSEService } from '../../services/enhanced-sse.service';
import {createLogger} from '../../utils/logger';
import { ContentSanitizationService } from '../../services/content-sanitization.service';
import { PromptBarService } from '../../services/prompt-bar-services/prompt-bar.service';
import { ToastService } from '../../services/toast.service';

// Interface for selected files (same as prompt-content)
interface SelectedFile {
  id: string;
  name: string;
  url: string;
  type: string;
}

// ENHANCEMENT: Prompt bar loading state interface
export interface PromptBarLoadingState {
  isVisible: boolean;
  phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY' | null;
  status: 'IN_PROGRESS' | 'COMPLETED' | null;
  loadingText: string;
  isInitialGeneration: boolean;
  isRegeneration: boolean;
}

@Component({
  selector: 'app-chat-window',
  standalone: true,
  imports: [CommonModule, PromptBarComponent, CardsComponent, MarkdownModule, VerticalStepperComponent, IconsComponent, GenerationAccordionComponent],
  templateUrl: './chat-window.component.html',
  styleUrls: ['./chat-window.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class ChatWindowComponent implements AfterViewChecked, OnInit, OnDestroy {
  private destroyRef = inject(DestroyRef);
  // Auto-scroll functionality removed
  private _showStepper: boolean = false;
  private logger = createLogger("ChatWindowComponent");

  // Stepper related properties
  showPreview: boolean = false;
  previewImage: { url: string, name: string } | null = null;
  private timeoutRefs: { [key: string]: any } = {};

  // Reference to the stepper component
  @ViewChild(VerticalStepperComponent) private stepperComponent?: VerticalStepperComponent;
  @Input()
  get showStepper(): boolean {
    return this._showStepper;
  }
  set showStepper(value: boolean) {
    if (this._showStepper !== value) {
      this._showStepper = value;
      // Manage stepper message in chat flow
      if (value) {
        this.addStepperMessage();
        // If stepper is shown and we have progress, update the stepper message
        if (this.progress) {
          this.updateStepperMessage();
        }
      } else {
        // CRITICAL FIX: Don't remove stepper message during regeneration
        // The stepper should remain in chat history during code-regen events
        const isRegenerationActive = this.stepperStateService.isRegenerationActive();
        if (!isRegenerationActive) {
          this.removeStepperMessage();
        } else {
          this.logger.info('🔒 Preserving stepper in chat during regeneration - not removing stepper message');
        }
      }
    }
  }

  private _progress: string = '';
  @Input()
  get progress(): string {
    return this._progress;
  }
  set progress(value: string) {
    if (this._progress !== value) {
      this._progress = value;
      // Update stepper message when progress changes
      if (this.showStepper && value) {
        this.updateStepperMessage();
        // Auto-scroll removed - let user control scrolling
      }
    }
  }

  private _progressDescription: string = '';
  @Input()
  get progressDescription(): string {
    return this._progressDescription;
  }
  set progressDescription(value: string) {
    if (this._progressDescription !== value) {
      this._progressDescription = value;
      // Update stepper message when progress description changes
      if (this.showStepper && value) {
        this.updateStepperMessage();
        // Auto-scroll removed - let user control scrolling
      }
    }
  }

  @Input() status: string = 'PENDING';

  private _selectedImageDataUri: string = '';
  @Input()
  get selectedImageDataUri(): string {
    return this._selectedImageDataUri;
  }
  set selectedImageDataUri(value: string) {
    if (this._selectedImageDataUri !== value) {
      this._selectedImageDataUri = value;
      // If we have a new image, update the chat messages to include it
      if (value) {
        this.addImageToLastUserMessage();
      }
      // OnPush: Input property changes automatically trigger change detection
    }
  }

  @Input() defaultText: string = '';
  @Input() leftIcons: { name: string; status: 'active' | 'default' | 'disable' }[] = [];
  @Input() rightIcons: { name: string; status: 'active' | 'default' | 'disable' }[] = [];
  @Input() isCodeGenerationComplete: boolean = false;
  @Input() isUIDesignLoading: boolean = false;
  @Input() isCodeGenerationLoading: boolean = false;
  @Input() codeRegenerationProgressDescription: string = '';

  // API integration inputs for vertical stepper
  @Input() projectId: string = '';
  @Input() jobId: string = '';
  @Input() useApi: boolean = false;

  // File upload state management (exact same as prompt-content)
  selectedFile: File | null = null;
  selectedFileName = '';
  selectedFiles: SelectedFile[] = [];
  isFileAttachDisabled = false;
  fileError = '';
  readonly maxAllowedFiles = 1;

  // Enhance prompt state management (exact same as prompt-content)
  isEnhancing = false;
  enhanceClickCount = 0;
  maxEnhanceClicks = 2;
  isPromptEnhanced = false;
  isEnhancedPromptValid = true;

  // Current prompt for enhancement (exact same as prompt-content)
  currentPrompt = '';

  // Submission data for regeneration payload
  submissionData: {
    prompt: string;
    timestamp: string;
    imageFile: File | null;
    imageUrl: string | null;
    imageDataUri: string | null;
    fileName: string | null;
  } = {
    prompt: '',
    timestamp: new Date().toISOString(),
    imageFile: null,
    imageUrl: null,
    imageDataUri: null,
    fileName: null,
  };



  constructor(
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
    private stepperStateService: StepperStateService,
    private contentSanitizationService: ContentSanitizationService,
    private promptService: PromptBarService,
    private toastService: ToastService,
    private regenerationIntegrationService: RegenerationIntegrationService,
    private enhancedSSEService: EnhancedSSEService
  ) {
    this.destroyRef = inject(DestroyRef);
  }
  private _theme: 'light' | 'dark' = 'light';
  @Input()
  get theme(): 'light' | 'dark' {
    return this._theme;
  }
  set theme(value: 'light' | 'dark') {
    this._theme = value;
    this.updateMessagesTheme();
    // Set flag to update card themes in next view check cycle
    this.shouldUpdateCardThemes = true;
    // Force update the theme on all cards when theme changes
    setTimeout(() => {
      this.forceUpdateCardThemes();
    }, 0);
  }

  private _chatMessages: {
    id?: string;
    text: string;
    from: 'user' | 'ai' | 'stepper';
    theme: 'light' | 'dark';
    imageDataUri?: string;
    showLoadingIndicator?: boolean;
    loadingPhase?: 'intro' | 'main' | 'completed' | 'error';
    mainAPIInProgress?: boolean;
    // ENHANCEMENT: Intro message support for code generation
    showIntroMessage?: boolean;
    // ENHANCEMENT: Typewriter effect support
    isTyping?: boolean;
    // ENHANCEMENT: Generation result support
    isGenerationResult?: boolean;
    generationResult?: GenerationResult;
    // ENHANCEMENT: Stepper support
    isStepper?: boolean;
  }[] = [];

  // ENHANCEMENT: Separate array for generation results as individual cards
  private _generationResults: GenerationResult[] = [];
  // Auto-scroll functionality removed

  // ENHANCEMENT: Prompt bar loading state management
  private promptBarLoadingStateSubject = new BehaviorSubject<PromptBarLoadingState>({
    isVisible: false,
    phase: null,
    status: null,
    loadingText: '',
    isInitialGeneration: false,
    isRegeneration: false
  });
  public readonly promptBarLoadingState$ = this.promptBarLoadingStateSubject.asObservable();

  // Getter for current loading state
  get currentPromptBarLoadingState(): PromptBarLoadingState {
    return this.promptBarLoadingStateSubject.value;
  }

  /**
   * Get dynamic loading text based on progress phase and status
   * @param phase The current progress phase
   * @param status The current status
   * @returns The appropriate loading text
   */
  private getLoadingTextForProgress(
    phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY' | null,
    status: 'IN_PROGRESS' | 'COMPLETED' | null
  ): string {
    if (!phase || status !== 'IN_PROGRESS') {
      return '';
    }

    switch (phase) {
      case 'CODE_GENERATION':
        return 'Generating...';
      case 'BUILD':
        return 'Building...';
      case 'DEPLOY':
        return 'Deploying...';
      default:
        return 'Processing...';
    }
  }

  /**
   * Update prompt bar loading state
   * @param updates Partial updates to apply to the loading state
   */
  private updatePromptBarLoadingState(updates: Partial<PromptBarLoadingState>): void {
    const currentState = this.promptBarLoadingStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.promptBarLoadingStateSubject.next(newState);
    this.logger.info('🔄 Prompt bar loading state updated:', newState);
  }

  /**
   * Show prompt bar loading indicator
   * @param phase The progress phase
   * @param status The current status
   * @param isInitialGeneration Whether this is initial generation
   * @param isRegeneration Whether this is regeneration
   */
  private showPromptBarLoading(
    phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY',
    status: 'IN_PROGRESS' | 'COMPLETED',
    isInitialGeneration: boolean = false,
    isRegeneration: boolean = false
  ): void {
    const loadingText = this.getLoadingTextForProgress(phase, status);
    const shouldShow = status === 'IN_PROGRESS' && loadingText !== '';

    this.updatePromptBarLoadingState({
      isVisible: shouldShow,
      phase,
      status,
      loadingText,
      isInitialGeneration,
      isRegeneration
    });
  }

  /**
   * Hide prompt bar loading indicator
   */
  private hidePromptBarLoading(): void {
    this.updatePromptBarLoadingState({
      isVisible: false,
      phase: null,
      status: null,
      loadingText: '',
      isInitialGeneration: false,
      isRegeneration: false
    });
  }

  /**
   * Check if prompt bar loading indicator should be shown
   * @returns true if loading indicator should be visible
   */
  shouldShowPromptBarLoadingIndicator(): boolean {
    return this.currentPromptBarLoadingState.isVisible;
  }

  /**
   * Adds the selected image to the last user message in the chat
   * If no user message exists, it creates a new one
   */
  private addImageToLastUserMessage(): void {
    if (!this._selectedImageDataUri) return;

    // Find the last user message
    const lastUserMessageIndex = [...this._chatMessages].reverse().findIndex(msg => msg.from === 'user');

    if (lastUserMessageIndex >= 0) {
      // Convert reverse index to actual index
      const actualIndex = this._chatMessages.length - 1 - lastUserMessageIndex;
      // Update the last user message to include the image
      this._chatMessages[actualIndex].imageDataUri = this._selectedImageDataUri;
    } else {
      // If no user message exists, create a new one with the image
      this._chatMessages.push({
        text: '',
        from: 'user',
        theme: this.theme,
        imageDataUri: this._selectedImageDataUri
      });
    }

    // Auto-scroll removed - let user control scrolling
  }

  /**
   * Updates the theme of all messages to match the component's theme
   * Note: We're now using the component's theme directly in the template,
   * but we'll keep this method for backward compatibility
   */
  private updateMessagesTheme(): void {
    if (this._chatMessages && this._chatMessages.length > 0) {
      this._chatMessages.forEach(message => {
        message.theme = this._theme;
      });
    }

    // Force change detection to update the view
    setTimeout(() => {
      // This timeout ensures the theme changes are applied after the current execution cycle
    }, 0);
  }

  @Input()
  get chatMessages(): {
    id?: string;
    text: string;
    from: 'user' | 'ai' | 'stepper';
    theme: 'light' | 'dark';
    imageDataUri?: string;
    showLoadingIndicator?: boolean;
    loadingPhase?: 'intro' | 'main' | 'completed' | 'error';
    mainAPIInProgress?: boolean;
    // ENHANCEMENT: Intro message support for code generation
    showIntroMessage?: boolean;
    // ENHANCEMENT: Typewriter effect support
    isTyping?: boolean;
    // ENHANCEMENT: Generation result support
    isGenerationResult?: boolean;
    generationResult?: GenerationResult;
    // ENHANCEMENT: Stepper support
    isStepper?: boolean;
  }[] {
    return this._chatMessages;
  }

  // ENHANCEMENT: Getter for generation results
  get generationResults(): GenerationResult[] {
    return this._generationResults;
  }
  set chatMessages(value: {
    id?: string;
    text: string;
    from: 'user' | 'ai' | 'stepper';
    theme: 'light' | 'dark';
    imageDataUri?: string;
    showLoadingIndicator?: boolean;
    loadingPhase?: 'intro' | 'main' | 'completed' | 'error';
    mainAPIInProgress?: boolean;
    // ENHANCEMENT: Intro message support for code generation
    showIntroMessage?: boolean;
    // ENHANCEMENT: Typewriter effect support
    isTyping?: boolean;
    // ENHANCEMENT: Generation result support
    isGenerationResult?: boolean;
    generationResult?: GenerationResult;
    // ENHANCEMENT: Stepper support
    isStepper?: boolean;
  }[]) {
    // Check if messages have been added or changed
    const messagesAdded = value && this._chatMessages && value.length !== this._chatMessages.length;

    // Check if the last message content has changed (for streaming responses)
    let lastMessageChanged = false;
    if (value && this._chatMessages && value.length > 0 && this._chatMessages.length > 0) {
      const newLastMessage = value[value.length - 1];
      const oldLastMessage = this._chatMessages[this._chatMessages.length - 1];
      if (
        newLastMessage &&
        oldLastMessage &&
        newLastMessage.from === oldLastMessage.from &&
        newLastMessage.text !== oldLastMessage.text
      ) {
        lastMessageChanged = true;
      }
    }

    // Auto-scroll removed - let user control scrolling

    // Ensure each message has the correct theme
    if (value) {
      value.forEach(message => {
        if (!message.theme) {
          message.theme = this.theme;
        }
      });
    }

    this._chatMessages = value;



    // If new messages were added or content changed, force update the theme on all cards
    if (messagesAdded || lastMessageChanged) {
      // Use a shorter timeout for better responsiveness
      setTimeout(() => {
        this.forceUpdateCardThemes();
        // Auto-scroll removed - let user control scrolling
      }, 10); // Shorter delay for better responsiveness
    }
  }

  private _textValue: string = '';
  @Input()
  get textValue(): string {
    return this._textValue;
  }
  set textValue(value: string) {
    this._textValue = value;
    this.currentPrompt = value; // Sync with currentPrompt for enhancement
    this.textValueChange.emit(this._textValue);
  }

  @Output() textValueChange = new EventEmitter<string>();
  @Output() enterPressed = new EventEmitter<void>();
  @Output() iconClicked = new EventEmitter<{
    name: string;
    side: string;
    index: number;
    theme: string;
  }>();
  @Output() imageTransferred = new EventEmitter<void>();
  @Output() retryStep = new EventEmitter<number>();
  @Output() regenerationPayload = new EventEmitter<{ prompt: string; image: string[] }>();
  @Output() userMessageData = new EventEmitter<{
    prompt: string;
    images: Array<{ url: string; name: string; id: string }>;
    timestamp: string;
  }>();

  @ViewChild('chatScrollContainer') private chatScrollContainer!: ElementRef;

  handleIconClick(event: { name: string; side: string; index: number }): void {
    // If code generation is not complete, don't allow icon clicks
    if (!this.isCodeGenerationComplete) {
      return;
    }

    this.iconClicked.emit({ ...event, theme: this.theme });
  }

  /**
   * Handle file attach icon click (simple icon, no pill)
   */
  handleFileAttach(): void {
    if (this.isEnhancing || this.isFileAttachDisabled || this.isCodeGenerationLoading) {
      return;
    }
    this.handleEnhancedAlternate();
  }

  /**
   * Check if user can send request (validates intent and generation state)
   */
  canSendRequest(): boolean {
    // Don't allow sending during generation/loading
    if (this.isCodeGenerationLoading || this.isEnhancing) {
      return false;
    }

    // Don't allow sending if code generation is not complete
    if (!this.isCodeGenerationComplete) {
      return false;
    }

    // Don't allow sending if no text
    if (!this.textValue || this.textValue.trim() === '') {
      return false;
    }

    // Don't allow sending if prompt is enhanced but intent is "no"
    if (this.isPromptEnhanced && !this.isEnhancedPromptValid) {
      return false;
    }

    return true;
  }

  /**
   * Handle enhanced send (exact same as prompt-content with payload support)
   */
  handleEnhancedSend(): void {
    if (!this.canSendRequest()) {
      if (this.isPromptEnhanced && !this.isEnhancedPromptValid) {
        this.toastService.warning('Please enhance the prompt again for better results before sending.');
      }
      return;
    }

    // Prepare submission data with image payload for regeneration
    this.prepareSubmissionData();

    // Emit user message data for chat cards display
    const userMessageData = this.getUserMessageData();
    this.userMessageData.emit(userMessageData);

    // Emit regeneration payload for parent components to use
    const payload = this.getRegenerationPayload();
    this.regenerationPayload.emit(payload);

    // Clear prompt and images after successful submission
    this.clearAfterSubmission();

    this.enterPressed.emit();
  }

  /**
   * Prepare submission data for regeneration with image payload
   */
  private prepareSubmissionData(): void {
    this.submissionData = {
      prompt: this.currentPrompt,
      timestamp: new Date().toISOString(),
      imageFile: this.selectedFile,
      imageUrl: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null,
      imageDataUri: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null,
      fileName: this.selectedFileName,
    };

    // Update prompt service with current data for regeneration
    this.promptService.setPrompt(this.currentPrompt);
    if (this.selectedFile) {
      this.promptService.setImage(this.selectedFile);
    }
  }

  /**
   * Get regeneration payload in the format: {"prompt": "", "image": []}
   */
  getRegenerationPayload(): { prompt: string; image: string[] } {
    const imageDataUris = this.getImageDataUris();
    return {
      prompt: this.currentPrompt || this.textValue || '',
      image: imageDataUris
    };
  }

  /**
   * Get icon color based on theme and state (exact same as prompt-content)
   */
  getIconColor(): string {
    return `var(--icon-enabled-color)`;
  }

  /**
   * Truncate file name for display (exact same as prompt-content)
   */
  truncateFileName(fileName: string): string {
    const maxLength = 20;
    if (fileName.length <= maxLength) return fileName;

    const extension = fileName.split('.').pop() || '';
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4);

    return `${truncatedName}...${extension}`;
  }

  /**
   * Show file preview (exact same as prompt-content)
   */
  showFilePreview(file: SelectedFile): void {
    this.previewImage = file;
    this.showPreview = true;
  }

  /**
   * Shows the image preview overlay
   * @param imageUrl URL of the image to preview
   * @param imageName Name of the image (optional)
   */
  showImagePreview(imageUrl: string, imageName: string = 'Image'): void {
    this.previewImage = { url: imageUrl, name: imageName };
    this.showPreview = true;
    // OnPush: Property changes trigger change detection automatically
  }

  /**
   * Closes the image preview overlay
   */
  closeImagePreview(): void {
    this.showPreview = false;
    this.previewImage = null;
    // OnPush: Property changes trigger change detection automatically
  }



  /**
   * Sanitizes message text for safe markdown rendering
   * @param text The message text to sanitize
   * @returns Sanitized text safe for markdown rendering
   */
  getSanitizedMessageText(text: string): string {
    return this.contentSanitizationService.sanitizeForMarkdown(text);
  }

  /**
   * Check if we should show the UI Design loading indicator
   * Returns the value of the isUIDesignLoading input property
   */
  shouldShowUIDesignLoadingIndicator(): boolean {
    return this.isUIDesignLoading;
  }

  /**
   * Check if we should show the Code Generation loading indicator
   * Returns true when standard code generation/regeneration is in progress (not UI Design mode)
   * ENHANCEMENT: Provides loading indicator for standard code generation and regeneration processes
   */
  shouldShowCodeGenerationLoadingIndicator(): boolean {
    return this.isCodeGenerationLoading;
    
  }

  /**
   * Check if regeneration progress description should be shown
   * ENHANCEMENT: Shows regeneration progress above prompt bar
   */
  shouldShowCodeRegenerationProgressDescription(): boolean {
    return !!(this.codeRegenerationProgressDescription &&
              this.codeRegenerationProgressDescription.trim() !== '' &&
              this.isCodeGenerationLoading);
  }



  /**
   * Handle step updates from the vertical stepper component
   * @param stepIndex The index of the updated step
   */
  onStepUpdated(stepIndex: number): void {
    // You can add additional logic here if needed
  }

  /**
   * Handle retry button click from the vertical stepper component
   * @param stepIndex The index of the step to retry
   */
  onRetryStep(stepIndex: number): void {

    // Change the status to IN_PROGRESS to show loading animation
    this.status = 'IN_PROGRESS';

    // Emit the retry event to the parent component
    this.retryStep.emit(stepIndex);

    // OnPush: Property changes and event emissions trigger change detection automatically
  }

  // Auto-scroll methods removed

  /**
   * Add stepper as a chat message for proper flow
   * This method should be called when stepper becomes active
   */
  addStepperMessage(): void {
    // Check if stepper message already exists
    const stepperExists = this._chatMessages.some(msg => msg.isStepper);
    if (!stepperExists) {
      this._chatMessages.push({
        text: '', // Empty text since stepper handles content
        from: 'stepper',
        theme: this.theme,
        isStepper: true
      });
    }
  }

  /**
   * Remove stepper message from chat flow
   * This method should be called when stepper is no longer needed
   */
  removeStepperMessage(): void {
    this._chatMessages = this._chatMessages.filter(msg => !msg.isStepper);
  }

  /**
   * Add a new standalone AI message with unique ID
   * FIXED: Creates isolated AI cards for intro messages
   */
  public addStandaloneAIMessage(text: string, messageId?: string): string {
    const uniqueId = messageId || this.generateUniqueMessageId();

    const newMessage = {
      id: uniqueId,
      text: text,
      from: 'ai' as const,
      theme: this.theme,
      showIntroMessage: true,
      isTyping: false
    };

    this._chatMessages.push(newMessage);

    // Force update the theme on the new card
    setTimeout(() => {
      this.forceUpdateCardThemes();
    }, 10);

    this.logger.info('🆕 Added standalone AI message:', { messageId: uniqueId, textLength: text.length });
    return uniqueId;
  }

  /**
   * Update a specific message by ID
   * FIXED: Updates only the target message, not all messages
   */
  public updateMessageById(messageId: string, updates: Partial<any>): void {
    const messageIndex = this._chatMessages.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      this._chatMessages[messageIndex] = { ...this._chatMessages[messageIndex], ...updates };

      // Force update the theme on the updated card
      setTimeout(() => {
        this.forceUpdateCardThemes();
      }, 10);

      this.logger.debug('📝 Updated message by ID:', { messageId, updates });
    } else {
      this.logger.warn('⚠️ Message not found for update:', messageId);
    }
  }

  /**
   * Generate unique message ID
   */
  private generateUniqueMessageId(): string {
    return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Add generation result as a chat message for proper flow
   * This method can be called from parent components when code generation is completed
   */
  addGenerationResult(generationResult: GenerationResult): void {
    // Mark all previous generation results as not latest
    this.markPreviousResultsAsNotLatest();

    // Mark the new result as latest
    generationResult.isLatest = true;

    // Add generation result as a special chat message to maintain proper flow
    this._chatMessages.push({
      text: '', // Empty text since accordion handles content
      from: 'ai',
      theme: this.theme,
      isGenerationResult: true,
      generationResult: generationResult
    });

    // Also keep in separate array for backward compatibility
    this._generationResults.push(generationResult);

    // Force change detection
    this.cdr.detectChanges();
    // Auto-scroll removed - let user control scrolling
  }

  /**
   * Mark all previous generation results as not latest
   */
  private markPreviousResultsAsNotLatest(): void {
    // Update all existing generation results in chat messages
    this._chatMessages.forEach(message => {
      if (message.isGenerationResult && message.generationResult) {
        message.generationResult.isLatest = false;
      }
    });

    // Update all existing generation results in separate array
    this._generationResults.forEach(result => {
      result.isLatest = false;
    });
  }



  ngOnInit(): void {
    // Initialize component without auto-scroll functionality
    setTimeout(() => {
      // Ensure messages have the correct theme
      this.updateMessagesTheme();
      // Force update the theme on all cards
      this.forceUpdateCardThemes();
      // Auto-scroll functionality removed
    }, 0);

    // Subscribe to stepper reset events
    this.stepperStateService.resetStepper$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(shouldReset => {
        if (shouldReset) {
          this.resetStepperState();
        }
      });

    // ENHANCED: Setup regeneration integration
    this.setupRegenerationIntegration();
  }

  /**
   * Resets the stepper state
   * This is called when the stepper state service emits a reset event
   */
  private resetStepperState(): void {
    // Reset the stepper component if it exists
    if (this.stepperComponent) {
      this.stepperComponent.resetStepper();
    }

    // No need to remove stepper messages since stepper is now standalone

    // Reset the stepper inputs
    this._progress = '';
    this._progressDescription = '';
    this.status = 'PENDING';

    // OnPush: Property changes trigger change detection automatically
  }

  /**
   * Setup regeneration integration service subscriptions
   * ENHANCED: Perfect integration for regeneration flow with loading indicators
   */
  private setupRegenerationIntegration(): void {
    this.logger.info('🔗 Setting up regeneration integration in chat-window');

    // FIXED: Register this chat window component for intro message creation
    this.regenerationIntegrationService.setChatWindowComponent(this);

    // Subscribe to regeneration active state
    this.regenerationIntegrationService.regenerationActive$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(isActive => {
        this.logger.info('🔄 Regeneration active state changed:', { isActive });

        // Update loading indicators based on regeneration state
        if (isActive) {
          this.handleRegenerationStart();
        } else {
          this.handleRegenerationComplete();
        }
      });

    // Subscribe to UI state updates for loading indicators
    this.regenerationIntegrationService.uiStateUpdates$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(stateUpdate => {
        this.logger.info('🎛️ Handling UI state update in chat-window:', stateUpdate);
        this.handleRegenerationUIStateUpdate(stateUpdate);
      });

    // ENHANCED: Subscribe to initial generation progress events
    this.enhancedSSEService.currentProgress$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(progress => {
        this.logger.info('📊 Initial generation progress update:', progress);
        this.handleInitialGenerationProgress(progress);
      });

    // ENHANCED: Subscribe to initial generation status events
    this.enhancedSSEService.currentStatus$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(status => {
        this.logger.info('📈 Initial generation status update:', status);
        this.handleInitialGenerationStatus(status);
      });

    // ENHANCED: Subscribe to accordion creation events for error handling
    this.regenerationIntegrationService.accordionCreate$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(accordionInfo => {
        this.logger.info('📋 Handling accordion creation event:', accordionInfo);
        this.handleAccordionCreation(accordionInfo);
      });

    this.logger.info('✅ Regeneration integration setup complete in chat-window');
  }

  /**
   * Handle regeneration start
   */
  private handleRegenerationStart(): void {
    this.logger.info('🚀 Handling regeneration start in chat-window');

    // Show loading indicators
    this.isCodeGenerationLoading = true;
    this.codeRegenerationProgressDescription = 'Generating...';

    this.cdr.detectChanges();
  }

  /**
   * Handle regeneration complete
   */
  private handleRegenerationComplete(): void {
    this.logger.info('✅ Handling regeneration complete in chat-window');

    // Hide loading indicators
    this.isCodeGenerationLoading = false;
    this.codeRegenerationProgressDescription = '';

    this.cdr.detectChanges();
  }

  /**
   * Handle regeneration UI state updates
   * ENHANCED: Now also updates prompt bar loading state
   */
  private handleRegenerationUIStateUpdate(stateUpdate: any): void {
    this.logger.info('🎛️ Handling regeneration UI state update:', stateUpdate);

    // Update loading indicators based on phase and status
    if (stateUpdate.phase === 'CODE_GENERATION') {
      if (stateUpdate.status === 'IN_PROGRESS') {
        this.isCodeGenerationLoading = true;
        this.codeRegenerationProgressDescription = 'Generating...';
        // ENHANCED: Show prompt bar loading for regeneration
        this.showPromptBarLoading('CODE_GENERATION', 'IN_PROGRESS', false, true);
      } else if (stateUpdate.status === 'COMPLETED') {
        this.codeRegenerationProgressDescription = 'Editing...';
      }
    } else if (stateUpdate.phase === 'BUILD') {
      if (stateUpdate.status === 'IN_PROGRESS') {
        this.codeRegenerationProgressDescription = 'Building...';
        // ENHANCED: Show prompt bar loading for regeneration
        this.showPromptBarLoading('BUILD', 'IN_PROGRESS', false, true);
      } else if (stateUpdate.status === 'COMPLETED') {
        this.codeRegenerationProgressDescription = 'Deploying...';
      }
    } else if (stateUpdate.phase === 'DEPLOY') {
      if (stateUpdate.status === 'IN_PROGRESS') {
        this.codeRegenerationProgressDescription = 'Deploying...';
        // ENHANCED: Show prompt bar loading for regeneration
        this.showPromptBarLoading('DEPLOY', 'IN_PROGRESS', false, true);
      } else if (stateUpdate.status === 'COMPLETED') {
        this.isCodeGenerationLoading = false;
        this.codeRegenerationProgressDescription = '';
        // ENHANCED: Hide prompt bar loading when regeneration is complete
        this.hidePromptBarLoading();
      }
    }

    this.cdr.detectChanges();
  }

  /**
   * Handle initial generation progress updates
   * ENHANCED: Updates prompt bar loading state for initial generation
   */
  private handleInitialGenerationProgress(progress: string | null): void {
    this.logger.info('📊 Handling initial generation progress:', progress);

    if (!progress) return;

    // Map progress to phase for prompt bar loading
    let phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY' | null = null;

    if (progress === 'COMPONENTS_CREATED' || progress === 'FILES_GENERATED') {
      phase = 'CODE_GENERATION';
    } else if (progress === 'BUILD' || progress === 'BUILD_STARTED' || progress === 'BUILD_SUCCEEDED') {
      phase = 'BUILD';
    } else if (progress === 'DEPLOY' || progress === 'DEPLOYED' || progress === 'deployed') {
      phase = 'DEPLOY';
    }

    if (phase) {
      // Subscribe to current status to determine if we should show loading
      this.enhancedSSEService.currentStatus$
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe(currentStatus => {
          if (currentStatus === 'IN_PROGRESS') {
            this.showPromptBarLoading(phase!, 'IN_PROGRESS', true, false);
          }
        });
    }
  }

  /**
   * Handle initial generation status updates
   * ENHANCED: Updates prompt bar loading state for initial generation
   */
  private handleInitialGenerationStatus(status: string | null): void {
    this.logger.info('📈 Handling initial generation status:', status);

    if (!status) return;

    // Subscribe to current progress to determine phase
    this.enhancedSSEService.currentProgress$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(currentProgress => {
        let phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY' | null = null;

        // Map ProgressState values to our phase types
        if (currentProgress === 'COMPONENTS_CREATED' || currentProgress === 'FILES_GENERATED') {
          phase = 'CODE_GENERATION';
        } else if (currentProgress === 'BUILD' || currentProgress === 'BUILD_STARTED' || currentProgress === 'BUILD_SUCCEEDED') {
          phase = 'BUILD';
        } else if (currentProgress === 'DEPLOY' || currentProgress === 'DEPLOYED' || currentProgress === 'deployed') {
          phase = 'DEPLOY';
        }

        if (phase) {
          if (status === 'IN_PROGRESS') {
            this.showPromptBarLoading(phase, 'IN_PROGRESS', true, false);
          } else if (status === 'COMPLETED' && phase === 'DEPLOY') {
            // Hide loading when deployment is completed
            this.hidePromptBarLoading();
          }
        }
      });
  }

  /**
   * ENHANCED: Handle accordion creation events from regeneration integration service
   * Converts accordion info to GenerationResult and adds to chat messages
   */
  private handleAccordionCreation(accordionInfo: any): void {
    this.logger.info('📋 Processing accordion creation:', {
      hasFiles: !!accordionInfo.files,
      fileCount: accordionInfo.files?.length || 0,
      version: accordionInfo.version,
      projectName: accordionInfo.projectName,
      isError: accordionInfo.isError,
      hasErrorMessage: !!accordionInfo.errorMessage
    });

    // Convert accordion info to GenerationResult format
    const generationResult: GenerationResult = {
      type: accordionInfo.isError ? 'error' : 'success',
      version: accordionInfo.version || 1,
      projectName: accordionInfo.projectName || 'Generated Project',
      files: accordionInfo.files || [],
      timestamp: accordionInfo.timestamp || new Date(),
      isLatest: true, // New accordions are always latest
      // ENHANCED: Include error information for error accordions
      errorMessage: accordionInfo.errorMessage,
      projectId: accordionInfo.projectId,
      jobId: accordionInfo.jobId
    };

    // Add generation result to chat messages
    this.addGenerationResult(generationResult);

    this.logger.info('✅ Accordion added to chat messages:', {
      type: generationResult.type,
      version: generationResult.version,
      hasErrorMessage: !!generationResult.errorMessage
    });
  }

  // Auto-scroll mutation observer removed

  /**
   * Forces an update of the theme on all cards by directly manipulating the DOM
   * This is a fallback method to ensure the theme is applied correctly
   */
  private forceUpdateCardThemes(): void {
    setTimeout(() => {
      try {
        // Get all card elements within this component
        const cardElements = document.querySelectorAll('.chat-wrapper awe-cards .awe-card');

        // Apply the appropriate theme class to each card
        cardElements.forEach(card => {
          // Remove existing theme classes
          card.classList.remove('awe-card--light', 'awe-card--dark');

          // Add the current theme class
          card.classList.add(`awe-card--${this._theme}`);

          // Force the background color based on the card type and theme
          const isUserCard = card.closest('.user-card') !== null;
          const isAiCard = card.closest('.ai-card') !== null;

          if (this._theme === 'dark') {
            if (isUserCard) {
              (card as HTMLElement).style.backgroundColor = 'var(--color-background-dark)';
              (card as HTMLElement).style.color = 'var(--text-white)';
            } else if (isAiCard) {
              (card as HTMLElement).style.backgroundColor =
                'var(--color-background-secondary-light)';
              (card as HTMLElement).style.color = 'var(--text-white)';
            }
          } else {
            if (isUserCard) {
              (card as HTMLElement).style.backgroundColor =
                'var(--Neutral-N-100, var(--color-background-light))';
              (card as HTMLElement).style.color = 'var(--text-black)';
            } else if (isAiCard) {
              (card as HTMLElement).style.backgroundColor =
                'var(--Neutral-N-50, var(--color-background-light))';
              (card as HTMLElement).style.color = 'var(--text-black)';
            }
          }
        });
      } catch (error) {
        this.logger.error('Error updating card themes:', error);
      }
    }, 100); // Small delay to ensure DOM is ready
  }

  // Auto-scroll helper methods removed

  // Auto-scroll listener removed

  ngOnDestroy(): void {
    // Clear stepper timeouts
    this.clearAllTimeouts();
    // Auto-scroll cleanup removed

    // Note: Subscriptions are automatically cleaned up by takeUntilDestroyed
    // We can't easily remove the exact same scroll event listener we added,
    // but we've cleaned up the timeouts and observers which is the main concern for memory leaks
  }

  // We'll set up the scroll event listener in ngOnInit instead of using HostListener

  ngAfterViewChecked(): void {
    // Check if we need to update card themes (only do this occasionally to avoid performance issues)
    if (this.shouldUpdateCardThemes) {
      this.shouldUpdateCardThemes = false;
      this.forceUpdateCardThemes();
    }
    // Auto-scroll removed from view checked
  }

  // Auto-scroll methods removed

  // Flag to track if we need to update card themes
  private shouldUpdateCardThemes = false;

  // Auto-scroll method removed

  /**
   * Update stepper state - stepper is now integrated in chat flow
   * This method is called when the stepper state changes
   */
  updateStepperMessage(): void {
    // Ensure stepper message exists in chat flow when stepper is active
    if (this.showStepper) {
      this.addStepperMessage();
    }
    // Trigger change detection to update the inline stepper
    this.cdr.detectChanges();
  }

  // No longer need stepper-related methods as we're using the VerticalStepperComponent

  /**
   * Clear all timeouts
   */
  private clearAllTimeouts(): void {
    Object.values(this.timeoutRefs).forEach(timeoutId => clearTimeout(timeoutId));
    this.timeoutRefs = {};
  }

  /**
   * Handle file upload functionality (exact same as prompt-content)
   */
  private handleEnhancedAlternate(): void {
    if (this.isEnhancing || this.isCodeGenerationLoading) {
      return;
    }
    if (this.isFileAttachDisabled) {
      this.fileError = `Only 1 image can be uploaded at a time`;
      return;
    }
    const fileInput = this.createFileInput();
    fileInput.addEventListener('change', (event: Event) => this.handleFileSelect(event));
    fileInput.click();
  }

  /**
   * Create file input element (exact same as prompt-content)
   */
  private createFileInput(): HTMLInputElement {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);
    return fileInput;
  }

  /**
   * Handle file selection (exact same as prompt-content)
   */
  private handleFileSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];
    if (!file || !this.validateFile(file)) return;

    // Process file immediately for fast UI response
    this.processFileImmediately(file);

    document.body.removeChild(event.target as HTMLElement);
  }

  /**
   * Validate selected file (exact same as prompt-content)
   */
  private validateFile(file: File): boolean {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      this.toastService.error('Please select a valid image file (JPEG, PNG, GIF, WebP)');
      return false;
    }

    if (file.size > maxSize) {
      this.toastService.error('File size must be less than 10MB');
      return false;
    }

    return true;
  }

  /**
   * Process file immediately for fast UI response (exact same as prompt-content)
   */
  private processFileImmediately(file: File): void {
    // Reset enhancement state
    this.promptService.resetEnhancedPromptState();
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;

    // Create file URL immediately for instant preview
    const fileUrl = URL.createObjectURL(file);
    const newFile: SelectedFile = {
      id: Math.random().toString(36).substring(2, 11),
      name: file.name || `pasted-${Date.now()}.png`,
      url: fileUrl,
      type: file.type,
    };

    // Update UI immediately
    this.selectedFile = file;
    this.selectedFileName = newFile.name;
    this.selectedFiles = [newFile];
    this.updateFileAttachPillStatus();
    this.updateSendButtonState();

    // Force immediate UI update to show file preview
    this.cdr.detectChanges();

    // Update services
    this.promptService.setPrompt(this.textValue);
    this.promptService.setImage(file);

    // Process file data in background (slow part)
    const reader = new FileReader();
    reader.onload = e => this.handleFileReaderLoad(e, file, newFile, fileUrl);
    reader.readAsDataURL(file);

    // When image is uploaded, automatically append text instead of just changing placeholder
    if (!this.textValue || this.textValue.trim() === '') {
      // Automatically append a starter text for image-based prompts
      const imagePromptStarter = 'Create a website based on this image';
      this.textValue = imagePromptStarter;

      // Force UI update
      this.cdr.detectChanges();
    }

    // Always update the service with current prompt after processing
    this.promptService.setPrompt(this.textValue);
  }

  /**
   * Handle file reader load event (exact same as prompt-content)
   */
  private handleFileReaderLoad(e: ProgressEvent<FileReader>, file: File, newFile: SelectedFile, fileUrl: string): void {
    const imageDataUri = e.target?.result as string;

    // Update submission data with image information
    this.submissionData = {
      ...this.submissionData,
      prompt: this.currentPrompt,
      imageFile: file,
      imageUrl: fileUrl,
      imageDataUri,
      fileName: newFile.name,
    };

    // Reset enhancement state when new image is uploaded
    this.promptService.resetEnhancedPromptState();
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;

    // Update the file URL with the data URI for better performance
    newFile.url = imageDataUri;

    // Update prompt service
    this.promptService.setPrompt(this.currentPrompt);
    this.promptService.setImage(file);

    // Clean up object URL
    URL.revokeObjectURL(fileUrl);
  }

  /**
   * Update file attach pill status (exact same as prompt-content)
   */
  private updateFileAttachPillStatus(): void {
    this.isFileAttachDisabled = this.selectedFiles.length >= this.maxAllowedFiles;
  }

  /**
   * Update send button state (exact same as prompt-content)
   */
  private updateSendButtonState(): void {
    // Implementation for updating send button state
    this.cdr.detectChanges();
  }

  /**
   * Handle enhance text functionality (exact same as prompt-content)
   */
  handleEnhanceText(): void {
    if (this.enhanceClickCount >= this.maxEnhanceClicks ||
        !this.currentPrompt?.trim() ||
        this.isCodeGenerationLoading) {
      return;
    }

    this.isEnhancing = true;

    const imageDataUris = this.selectedFiles.length && this.submissionData.imageDataUri
      ? [this.submissionData.imageDataUri]
      : [];

    this.promptService.enhancePrompt(this.currentPrompt, 'Generate Application', imageDataUris)
      .subscribe({
        next: (response: any) => {
          let parsedResponse: any;
          try {
            // Handle response that might be wrapped in markdown code blocks
            let responseText = typeof response === 'string' ? response : JSON.stringify(response);

            // Check if response is wrapped in markdown code blocks
            const codeBlockMatch = responseText.match(/```(?:json)?\s*\n?([\s\S]*?)\n?```/);
            if (codeBlockMatch) {
              responseText = codeBlockMatch[1].trim();
            }

            parsedResponse = JSON.parse(responseText);
          } catch (error) {
            // If parsing fails, try to use the response as-is
            parsedResponse = typeof response === 'object' ? response : { prompt: response };
          }

          const { prompt, intent } = parsedResponse || {};
          if (!prompt) {
            return;
          }

          this.currentPrompt = prompt;
          this.textValue = prompt; // Update the text value
          this.promptService.setEnhancedPrompt(prompt);
          this.isPromptEnhanced = true;

          const normalizedIntent = intent?.toLowerCase() || '';
          if (normalizedIntent === 'yes') {
            this.isEnhancedPromptValid = true;
            this.enhanceClickCount++;
            this.toastService.success('Prompt enhanced successfully. Ready to generate code.');
          } else {
            this.isEnhancedPromptValid = false;
            this.toastService.warning('Prompt needs more details. Click enhance again for better results.');
          }
        },
        error: () => {
          this.isEnhancedPromptValid = true;
          this.toastService.error('Unable to enhance prompt. Please try again with more specific details.');
        },
        complete: () => {
          this.isEnhancing = false;
        }
      });
  }



  /**
   * Remove uploaded file (exact same as prompt-content)
   */
  removeFile(fileId: string): void {
    this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);

    if (this.selectedFiles.length === 0) {
      this.selectedFile = null;
      this.selectedFileName = '';
      this.isFileAttachDisabled = false;
      this.enhanceClickCount = 0;

      // Clear submission data
      this.submissionData = {
        prompt: this.currentPrompt,
        timestamp: new Date().toISOString(),
        imageFile: null,
        imageUrl: null,
        imageDataUri: null,
        fileName: null,
      };

      // Clear from prompt service
      this.promptService.setImage(null);
    }

    this.updateFileAttachPillStatus();
    this.updateSendButtonState();
    this.cdr.detectChanges();
  }

  /**
   * Get current submission data for regeneration payload
   */
  getSubmissionData(): any {
    return {
      prompt: this.currentPrompt,
      timestamp: this.submissionData.timestamp,
      imageFile: this.submissionData.imageFile,
      imageUrl: this.submissionData.imageUrl,
      imageDataUri: this.submissionData.imageDataUri,
      fileName: this.submissionData.fileName,
      isPromptEnhanced: this.isPromptEnhanced,
      isEnhancedPromptValid: this.isEnhancedPromptValid,
    };
  }

  /**
   * Get image data URIs for regeneration
   */
  getImageDataUris(): string[] {
    return this.submissionData.imageDataUri ? [this.submissionData.imageDataUri] : [];
  }

  /**
   * Get user message data with images for chat cards
   */
  getUserMessageData(): {
    prompt: string;
    images: Array<{
      url: string;
      name: string;
      id: string;
    }>;
    timestamp: string;
  } {
    return {
      prompt: this.currentPrompt || this.textValue || '',
      images: this.selectedFiles.map(file => ({
        url: file.url,
        name: file.name,
        id: file.id
      })),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Clear images after successful submission
   * This ensures proper state cleanup and prevents image data from persisting
   */
  private clearImagesAfterSubmission(): void {
    // Clear selected files array
    this.selectedFiles = [];

    // Clear individual file references
    this.selectedFile = null;
    this.selectedFileName = '';

    // Clear submission data
    this.submissionData = {
      ...this.submissionData,
      imageFile: null,
      imageUrl: null,
      imageDataUri: null,
      fileName: null,
    };

    // Clear image from prompt service
    this.promptService.setImage(null);

    // Update file attach pill status
    this.updateFileAttachPillStatus();

    // Trigger change detection to update UI
    this.cdr.detectChanges();

    this.logger?.info('🧹 Images cleared after successful submission');
  }

  /**
   * Clear current prompt and images after successful submission
   */
  clearAfterSubmission(): void {
    this.textValue = '';
    this.currentPrompt = '';
    this.selectedFiles = [];
    this.selectedFile = null;
    this.selectedFileName = '';
    this.isFileAttachDisabled = false;
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;

    // Clear submission data
    this.submissionData = {
      prompt: '',
      timestamp: new Date().toISOString(),
      imageFile: null,
      imageUrl: null,
      imageDataUri: null,
      fileName: null,
    };

    // Clear from prompt service
    this.promptService.setPrompt('');
    this.promptService.setImage(null);
  }

  // No longer need these methods as we're using the VerticalStepperComponent
}
