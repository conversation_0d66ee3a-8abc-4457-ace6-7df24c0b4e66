import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from './auth.service';
import { TokenStorageService } from './token-storage.service';
import { HttpErrorResponse } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class AuthTokenService {
  private tokenCheckInterval: any;
  private checkIntervalMs = 4000; // Check every 4 seconds
  private subscription = new Subscription();

  constructor(
    private router: Router,
    private authService: AuthService,
    private tokenStorageService: TokenStorageService,
  ) {}

  public handleAuthCodeAndToken(): void {
    const authCode = new URLSearchParams(window.location.search).get('code');
    const accessToken = this.tokenStorageService.getAccessToken();
    const refreshToken = this.tokenStorageService.getRefreshToken();
    const loginType = this.tokenStorageService.getLoginType();

    if (loginType === 'basic') {
      this.handleBasicLoginFlow(accessToken, refreshToken);
    } else {
      this.handleSSOFlow(accessToken, refreshToken, authCode);
    }
  }

  private handleBasicLoginFlow(
    accessToken: string | null,
    refreshToken: string | null,
  ): void {
    if (!accessToken && refreshToken) {
      this.handleBasicTokenRefresh();
    } else if (!accessToken && !refreshToken) {
      this.router.navigateByUrl('/login');
    }
  }

  private handleSSOFlow(
    accessToken: string | null,
    refreshToken: string | null,
    authCode: string | null,
  ): void {
    if (!accessToken && refreshToken) {
      this.router.navigateByUrl(`/callback?refresh_token=${refreshToken}`);
    } else if (!accessToken && !refreshToken && authCode) {
      this.router.navigateByUrl(`/callback?code=${authCode}`);
    }
  }

  public startTokenCheck(): void {
    this.tokenCheckInterval = setInterval(() => {
      const accessToken = this.tokenStorageService.getAccessToken();
      if (!accessToken) {
        const refreshToken = this.tokenStorageService.getRefreshToken();
        const loginType = this.tokenStorageService.getLoginType();
        if (refreshToken) {
          if (loginType === 'basic') {
            this.handleBasicTokenRefresh();
          } else {
            this.handleTokenRefresh(refreshToken);
          }
        } else {
          this.tokenStorageService.clearTokens();
          this.router.navigateByUrl('/login');
        }
      }
    }, this.checkIntervalMs);
  }

  private handleBasicTokenRefresh(): void {
    const refreshSub = this.authService.basicRefreshToken().subscribe({
      next: () => {
        console.info('Basic token refreshed successfully');
        this.router.navigateByUrl('/dashboard');
      },
      error: (err: HttpErrorResponse) => {
        this.tokenStorageService.clearTokens();

        console.error('Basic token refresh failed:', err);
        this.router.navigateByUrl('/login');
      },
    });
    this.subscription.add(refreshSub);
  }

  private handleTokenRefresh(refreshToken: string): void {
    const refreshSub = this.authService.refreshToken(refreshToken).subscribe({
      next: () => {
        console.info('Token refreshed successfully');
      },
      error: (err: HttpErrorResponse) => {
        this.tokenStorageService.clearTokens();
        console.error('Token refresh failed:', err);
        this.router.navigateByUrl('/login');
      },
    });
    this.subscription.add(refreshSub);
  }

  public stopTokenCheck(): void {
    if (this.tokenCheckInterval) {
      clearInterval(this.tokenCheckInterval);
    }
    this.subscription.unsubscribe();
  }
}
