import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class TokenStorageService {
  private ACCESS_TOKEN_KEY = 'access_token';
  private REFRESH_TOKEN_KEY = 'refresh_token';
  private DA_NAME_KEY = 'da_name';
  private DA_USERNAME_KEY = 'da_username';
  private DA_ID_TOKEN = 'id_token';
  private LOGIN_TYPE_KEY = 'login_type';

  private getCurrentDomain(): string {
    return window.location.hostname;
  }

  public storeLoginType(loginType: 'sso' | 'basic'): void {
    this.setCookie(this.LOGIN_TYPE_KEY, loginType);
  }

  public getLoginType(): 'sso' | 'basic' | null {
    const loginType = this.getCookie(this.LOGIN_TYPE_KEY);
    return loginType === 'sso' || loginType === 'basic' ? loginType : null;
  }

  public setCookie(cname: string, cvalue: string, exseconds?: number): void {
    const d = new Date();
    let expires = "";
    if (exseconds) {
      d.setTime(d.getTime() + (exseconds * 1000));
      expires = "expires=" + d.toUTCString();
    }
    let secureFlag = window.location.protocol === "https:" ? ";Secure" : "";
    let sameSiteFlag = ";SameSite=Lax";
    let domainFlag = `;domain=${this.getCurrentDomain()}`;
    document.cookie = `${cname}=${cvalue};${expires};path=/;${secureFlag}${sameSiteFlag}${domainFlag}`;
  }

  private getCookie(name: string): string | null {
    const match = document.cookie.match(new RegExp(`(^| )${name}=([^;]+)`));
    return match ? decodeURIComponent(match[2]) : null;
  }

  public deleteCookie(name: string): void {
    let domainFlag = `;domain=${this.getCurrentDomain()}`;
    document.cookie = `${name}=; path=/; max-age=0; Secure; SameSite=Strict${domainFlag}`;
  }

  storeTokens(accessToken: string, refreshToken: string, expiresInSeconds: number): void {
    this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);
    this.setCookie(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  storeAccessToken(accessToken: string, expiresInSeconds?: number): void {
    this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);
  }

  storeDaInfo(daName: string, daUsername: string, idToken: string): void {
    this.setCookie(this.DA_NAME_KEY, daName);
    this.setCookie(this.DA_USERNAME_KEY, daUsername);
    this.setCookie(this.DA_ID_TOKEN, idToken);
  }

  getAccessToken(): string | null {
    return this.getCookie(this.ACCESS_TOKEN_KEY);
  }

  getRefreshToken(): string | null {
    return this.getCookie(this.REFRESH_TOKEN_KEY);
  }

  getDaName(): string | null {
    return this.getCookie(this.DA_NAME_KEY);
  }

  getDaUsername(): string | null {
    return this.getCookie(this.DA_USERNAME_KEY);
  }

  getIdToken(): string | null {
    return this.getCookie(this.DA_ID_TOKEN);
  }

  clearTokens(): void {
    this.deleteCookie(this.ACCESS_TOKEN_KEY);
    this.deleteCookie(this.REFRESH_TOKEN_KEY);
    this.deleteCookie(this.DA_NAME_KEY);
    this.deleteCookie(this.DA_USERNAME_KEY);
    this.deleteCookie(this.DA_ID_TOKEN);
    this.deleteCookie(this.LOGIN_TYPE_KEY);
  }
}
