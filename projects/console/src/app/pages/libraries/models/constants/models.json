{"labels": {"name": "Model Name", "modelDeploymentName": "modelDeploymentName", "description": "Description", "modelType": "Model Type", "aiEngine": "AI Engine", "model": "Model", "modelName": "Model Engine", "baseurl": "Base URL", "llmDeploymentName": "LLM Deployment Name", "apiKey": "API Key Encoded", "apiVersion": "API Version", "awsAccessKey": "AWS Access Key", "awsSecretKey": "AWS Secret Key", "awsRegion": "AWS Region", "bedrockModelId": "Bedrock Model Id", "gcpProjectId": "GCP Project Id", "gcpLocation": "GCP Location", "vertexAIEndpoint": "VertexAI Endpoint", "serviceUrl": "Service URL", "apiKeyEncoded": "API Key Encoded", "headerName": "Header Name", "organization": "Organization", "domain": "Domain", "project": "Project", "team": "Team", "configure": "Configure Parameters", "generative": "Generative", "embedding": "Embedding", "exit": "Exit"}}